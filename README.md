# GPX 路線切分工具

這是一個專門處理登山路線 GPX 軌跡的工具，可以將路線按通訊點切分成多段，並提供完整的瀏覽和編輯功能。

## 主要功能

### 路線編輯界面 (frontend/)
- 互動式地圖顯示完整路線和所有點位
- 支援路線 A 和路線 B 的切換顯示
- 可以新增、刪除和編輯點位資料
- 提供點位表格檢視和排序功能
- 支援下載修改後的檔案
- **動態掃描** `data_work/` 資料夾，自動偵測可用路線

### 路線切分瀏覽界面 (網頁瀏覽/)
- 三層動態選單：路線版本 → 路線名稱 → 段落編號
- 專門瀏覽 `路線切分/` 資料夾中的切分後路線
- 互動式地圖顯示選中段落的路線和點位
- 段落統計資訊：起終點、點位數量、類型分布
- 點位詳細表格，點擊跳轉到地圖位置
- **動態掃描** `路線切分/` 資料夾，自動偵測可用路線和段落

### 路線處理工具
- 自動解析 GPX 軌跡檔案
- 整合通訊點資料到路線中
- 確保通訊點在路徑上而非只是地圖標記
- **自動插值功能**：對缺少時間和高度的點位進行智能插值
  - 基於地理距離比例進行線性插值
  - 時間插值：根據前後點位的時間和距離計算
  - 高度插值：根據前後點位的海拔和距離計算
- 根據最後通訊點分割成路線 A 和路線 B
- 在網頁上手動對路線進行點位增加與刪除

### 路線切分工具
- 依據 data_raw 中的通訊點檔案進行路線切分
- 按照通訊點順序將路線切分為多段
- 每段包含起始通訊點到結束通訊點之間的所有點位
- **內建插值功能**：在切分前自動補齊缺失的時間和高度資料
- 自動建立分類資料夾結構
- 分別匯出 TXT 和 GeoJSON 格式檔案

### GeoJSON 轉 GPX 工具
- 將 `data_work/` 中的完整路線轉換為 GPX 格式
- 自動掃描所有路線版本（route_a 和 route_b）
- **智能插值處理**：轉換前自動補齊缺失的時間和高度資料
- 保留軌跡點的座標、海拔和時間資訊
- 生成航點標記重要位置（通訊點等）
- 輸出標準 GPX 1.1 格式檔案

## 檔案結構

```
gpx_point-_correction_tool/
├── data_raw/                    # 原始資料
│   ├── gpx/                     # 原始 GPX 軌跡檔案
│   └── txt/                     # 通訊點座標檔案
├── data_work/                   # 處理後資料
│   ├── route_a/                 # 路線 A 的完整資料
│   └── route_b/                 # 路線 B 的完整資料
│       └── 路線名稱/
│           ├── points.txt       # 點位檔案
│           └── route.geojson    # 路線檔案
├── 路線切分/                     # 切分後資料
│   ├── route_a/                 # 路線 A 切分資料
│   └── route_b/                 # 路線 B 切分資料
│       ├── geojson/路線名稱/     # GeoJSON 格式切分檔案
│       └── txt/路線名稱/         # TXT 格式切分檔案
├── frontend/                    # 路線編輯界面
│   ├── index.html              # 主要網頁檔案
│   ├── main.js                 # 前端互動邏輯
│   └── style.css               # 網頁樣式設定
├── 網頁瀏覽/                     # 路線切分瀏覽界面
│   ├── index.html              # 瀏覽界面網頁
│   ├── main.js                 # 瀏覽界面邏輯
│   └── style.css               # 瀏覽界面樣式
├── 修改好的gpx/                  # GeoJSON 轉 GPX 輸出資料夾
│   ├── 路線名稱_route_a.gpx      # 路線 A 的 GPX 檔案
│   └── 路線名稱_route_b.gpx      # 路線 B 的 GPX 檔案
└── scripts/                     # 處理程式
    ├── pt_process.py           # 主要路線處理程式
    ├── route_splitter.py       # 路線切分程式
    ├── geojson_to_gpx.py       # GeoJSON 轉 GPX 工具
    ├── utils.py                # 共用工具函數
    └── update_route_api.py     # 路線更新 API
```

## 使用流程

### 步驟 1：準備資料 (data_raw/)
1. 將 GPX 軌跡檔案放入 `data_raw/gpx/` 資料夾
2. 將對應的通訊點 TXT 檔案放入 `data_raw/txt/` 資料夾
3. 確保檔案名稱一致，例如：`北大武山.gpx` 和 `北大武山.txt`

### 步驟 2：處理路線 (pt_process.py → data_work/)
```bash
python scripts/pt_process.py
```
- 程式會自動整合通訊點到路線中
- **自動執行插值**：補齊缺失的時間和高度資料
- 生成包含所有點位的完整路線資料
- 分割成路線 A 和路線 B
- 輸出到 `data_work/` 資料夾

### 步驟 3：檢視編輯 (frontend/)
開啟 `frontend/index.html` 使用路線編輯界面：
- 查看完整的路線 A 或路線 B
- 編輯、新增或刪除點位資料
- 下載修改後的檔案

### 步驟 4：切分路線 (route_splitter.py → 路線切分/)
```bash
python scripts/route_splitter.py
```
- 程式會讀取 `data_raw` 中的通訊點資料
- **執行插值處理**：確保所有點位都有完整的時間和高度資料
- 按照通訊點順序切分路線為多段
- 自動建立分類資料夾並匯出檔案
- 輸出到 `路線切分/` 資料夾

### 步驟 5：切分路線瀏覽（可選擇）
開啟 `網頁瀏覽/index.html` 使用路線切分瀏覽界面：
- 使用三層選單選擇要檢視的路線段落
- 查看段落統計資訊和詳細點位
- 專門用於檢視切分後的路線段落

### 步驟 6：轉換 GPX (geojson_to_gpx.py → 修改好的gpx/)
```bash
python scripts/geojson_to_gpx.py
```
- 將 `data_work/` 中的路線轉換為標準 GPX 格式
- **自動插值處理**：確保所有軌跡點都有完整的時間和高度資料
- 輸出到 `修改好的gpx/` 資料夾
- 檔名格式：`路線名稱_route_a.gpx`、`路線名稱_route_b.gpx`

## 檔案格式說明

### 輸入格式
- **GPX 檔案**：包含軌跡點的時間、座標和高度資訊
- **TXT 檔案**：制表符分隔，包含通訊點的座標和名稱

### 輸出格式
- **GeoJSON**：標準地理資料格式，包含路線幾何和點位屬性
- **TXT**：制表符分隔的點位清單，包含完整的點位資訊
- **GPX**：標準 GPS 軌跡格式，包含軌跡點和航點
- **切分檔案**：按段落命名，例如 `北大武山_切分好的_route_a_part1_points.txt`

## 技術規格

- **開發語言**：Python 和 JavaScript
- **地圖顯示**：Leaflet 地圖庫
- **資料處理**：GeoPandas 和 Pandas
- **距離計算**：Haversine 公式計算地理距離
- **插值演算法**：基於距離比例的線性插值
- **座標系統**：WGS84 經緯度座標
- **檔案編碼**：UTF-8 確保中文正確顯示

## 重要注意事項

- **檔案命名**：GPX 和 TXT 檔案必須使用相同的檔案名稱
- **資料格式**：通訊點 TXT 檔案必須符合指定的欄位格式
- **插值處理**：所有工具都會自動對缺失的時間和高度進行插值計算
- **處理時間**：大型軌跡檔案處理時間較長，請耐心等待
- **資料備份**：建議在處理前先備份原始資料檔案
- **路徑整合**：所有通訊點都會整合到路徑中，而非只是地圖標記

## 故障排除

### 網頁瀏覽工具顯示 JSON 錯誤
如果在使用 `網頁瀏覽/index.html` 時遇到 JSON 解析錯誤，請：
1. 重新執行 `python scripts/route_splitter.py`
2. 確保 `路線切分/` 資料夾中的檔案是最新的
3. 檢查瀏覽器 console 是否有其他錯誤訊息

### 路線切分結果不正確
請確認：
1. `data_raw/txt/` 中的通訊點檔案格式正確
2. 通訊點座標與 GPX 軌跡路徑接近
3. 執行順序：先執行 `pt_process.py`，再執行 `route_splitter.py`
