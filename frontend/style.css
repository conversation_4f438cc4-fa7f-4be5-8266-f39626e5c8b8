body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

header {
    background-color: #fff;
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

header h1 {
    margin: 0;
    font-size: 24px;
}

.controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.controls label {
    font-weight: bold;
}

.controls select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

.route-options button {
    padding: 10px 20px;
    margin-left: 10px;
    cursor: pointer;
    border: 1px solid #ccc;
    background-color: #f0f0f0;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.route-options button:hover {
    background-color: #e0e0e0;
}

.route-options button.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

main {
    display: flex;
    flex-grow: 1;
    padding: 20px;
    gap: 20px;
}

#map {
    flex: 2;
    height: 80vh;
    background-color: #eee;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#point-list-container {
    flex: 1;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

#point-list-container h2 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

/* 操作按鈕樣式 */
.point-operations {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.point-operations button {
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.modification-status {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
}

.modification-status.has-changes {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* 彈出視窗樣式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    transition: color 0.3s ease;
}

.close:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
}

/* 表單樣式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

/* 表格選擇功能樣式 */
#point-table th:first-child,
#point-table td:first-child {
    width: 50px;
    text-align: center;
}

.point-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.selected-row {
    background-color: #e3f2fd !important;
}

#point-table {
    width: 100%;
    margin-top: 15px;
}

#save-txt {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

/* 響應式設計 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        gap: 15px;
    }
    
    main {
        flex-direction: column;
        gap: 15px;
    }
    
    #map {
        height: 400px;
    }
    
    .point-operations {
        text-align: center;
    }
    
    .point-operations button {
        margin: 5px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

/* 其他樣式... */

.highlight {
    background-color: #e6f7ff;
    /* 淺藍色背景 */
    font-weight: bold;
}

</style>