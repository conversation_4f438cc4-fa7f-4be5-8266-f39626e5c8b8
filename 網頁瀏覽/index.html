<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPX 檔案瀏覽工具</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <header>
        <h1>GPX 檔案瀏覽工具</h1>
        <div class="route-selectors">
            <div class="selector-group">
                <label for="route-name">GPX 檔案：</label>
                <select id="route-name">
                    <option value="">掃描中...</option>
                </select>
            </div>
        </div>
    </header>

    <main>
        <div id="map"></div>
        <div id="info-panel">
            <div id="segment-info">
                <h2>檔案資訊</h2>
                <div id="segment-details">
                    <p>請選擇要檢視的 GPX 檔案</p>
                </div>
            </div>

            <div id="point-list-container">
                <h3>點位列表</h3>
                <table id="point-table" class="display">
                    <thead>
                        <tr>
                            <th>順序</th>
                            <th>緯度</th>
                            <th>經度</th>
                            <th>海拔（約）</th>
                            <th>類型</th>
                            <th>名稱</th>
                            <th>時間</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script type="text/javascript" charset="utf8"
        src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="main.js"></script>
</body>

</html>